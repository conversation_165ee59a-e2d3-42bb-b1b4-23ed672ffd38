{"cells": [{"cell_type": "code", "execution_count": null, "id": "59b403c6-4d68-4376-bb83-655b7bf11017", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "markdown", "id": "bc5be4db-1c5e-493a-bcae-f9a971217e3b", "metadata": {}, "source": ["## Reads the csv file \n", "It contains the metrics for several methods and datasets (obtained with format_metric notebook)"]}, {"cell_type": "code", "execution_count": null, "id": "8300197a-5cd2-459b-bd0d-93b02c278294", "metadata": {"tags": []}, "outputs": [], "source": ["da = pd.read_csv(\"metrics.csv\")\n", "\n", "da.head"]}, {"cell_type": "markdown", "id": "9cd34479-b101-45cc-ae70-7cac2dfbf91a", "metadata": {}, "source": ["## Computes a mean value for metrics across folds"]}, {"cell_type": "code", "execution_count": null, "id": "c565b69d-c737-42fc-ba32-39d7642a7251", "metadata": {"tags": []}, "outputs": [], "source": ["db = da.groupby([\"Model\", \"Dataset\", \"Division type\", \"Metric\", \"Mode\"]).agg(mean_value=(\"Value\", \"mean\")).reset_index()\n", "\n", "db.head"]}, {"cell_type": "markdown", "id": "a6496a89-9c6d-4267-8666-60ffed8194a3", "metadata": {}, "source": ["## Text formating for output"]}, {"cell_type": "code", "execution_count": null, "id": "4e6dcd5f-a3f2-4bb6-bba8-74bb2058b191", "metadata": {"tags": []}, "outputs": [], "source": ["mapping_dict = {\"ae\": \"AE\",\n", "               \"brits\": \"BRITS\",\n", "               \"locf\": \"LOCF\",\n", "               \"mrnn\": \"mRNN\",\n", "               \"rf\": \"RF\",\n", "               \"saits\": \"SAITS\",\n", "               \"svm\": \"SVM\",\n", "               \"transformer\": \"Transformer\",\n", "               \"unet\": \"U-Net\",\n", "               \"xgboost\": \"XGBoost\"}\n", "db[\"Model\"] = db[\"Model\"].replace(mapping_dict)\n", "\n", "mapping_dict_mode = {\"block.20\": \"Block 20\",\n", "                    \"block.100\": \"Block 100\",\n", "                    \"single\": \"Single\",\n", "                    \"profile\": \"Profile\"}\n", "db[\"Mode\"] = db[\"Mode\"].replace(mapping_dict_mode)\n", "\n", "mapping_dict_dataset = {\"taranaki\": \"Taranaki\",\n", "                       \"geolink\": \"Geolink\",\n", "                       \"teapot\": \"Teapot\"}\n", "db[\"Dataset\"] = db[\"Dataset\"].replace(mapping_dict_dataset)\n", "\n", "db"]}, {"cell_type": "markdown", "id": "8cf19926-ddeb-4519-bca0-458de537e330", "metadata": {}, "source": ["## Creates graphics for each metric\n", "Plots for all four dataset and at different missing patterns"]}, {"cell_type": "code", "execution_count": null, "id": "e7e6156d-efc0-4ea7-b411-c19cdacfc805", "metadata": {"tags": []}, "outputs": [], "source": ["metrics = db[\"Metric\"].unique()\n", "\n", "order_model = [\"LOCF\", \"AE\", \"SAITS\", \"BRITS\", \"RF\",\n", "               \"XGBoost\", \"Transformer\", \"U-Net\"]#, \"mRNN\", \"SVM\"]\n", "\n", "order_mode = [\"Single\", \"Block 20\", \"Block 100\", \"Profile\"]\n", "\n", "\n", "palette = sns.color_palette(['darkgray', 'blueviolet', 'dodgerblue', 'lightskyblue',\n", "                            'forestgreen', 'palegreen', 'darkorange', 'gold'])#, 'crimson', 'royalblue'])\n", "\n", "plots = list()\n", "sns.set(style=\"whitegrid\", font_scale=2)\n", "\n", "for i, metric in enumerate(metrics):\n", "    g = sns.catplot(x='Mode',\n", "                    y='mean_value',\n", "                    hue='Model',\n", "                    col='Dataset',\n", "                    kind='bar',\n", "                    data=db[db[\"Metric\"] == metric],\n", "                    order = order_mode,\n", "                    hue_order = order_model,\n", "                    height=6,\n", "                    aspect=1.5,\n", "                    sharey=True,\n", "                    palette=palette,\n", "                    col_wrap = 2)\n", "    g.set_axis_labels('Mode', f'Mean {metric.upper()}')\n", "    g.set_titles('{col_name}')\n", "    sns.move_legend(g, \"lower center\", ncol=5, bbox_to_anchor=(.5, 1))\n", "    plots.append(g)\n", "    plots[i].savefig(f\"figs/{metric}.png\")"]}, {"cell_type": "markdown", "id": "7ea03c79-7809-44eb-b3ce-d617bba70b23", "metadata": {}, "source": ["### Generating a .tex table (could be useful)"]}, {"cell_type": "code", "execution_count": null, "id": "868e627b-cf72-448b-b4d8-6e4146631881", "metadata": {"tags": []}, "outputs": [], "source": ["table = da.groupby([\"Dataset\", \"Mode\", \"Metric\", \"Model\"]).agg(mean_value=(\"Value\", \"mean\"))\n", "\n", "with open('output.tex', 'w') as f:\n", "    f.write(table.to_latex())\n", "\n"]}, {"cell_type": "markdown", "id": "16e5840c-a3cd-4736-81de-e28e31518646", "metadata": {"tags": []}, "source": ["## Preparation to compute the correlation between metrics"]}, {"cell_type": "code", "execution_count": null, "id": "789965ca-31f2-4932-bf92-686b886a3c14", "metadata": {"tags": []}, "outputs": [], "source": ["dclean = da[da.Mode != 'time']\n", "dclean = dclean[[\"Dataset\", \"Mode\", \"Model\", \"Fold\", 'Metric', 'Value']]\n", "dclean.head()"]}, {"cell_type": "code", "execution_count": null, "id": "28f0bbf7-12a7-4ddf-a42f-c6d7ad86a388", "metadata": {"tags": []}, "outputs": [], "source": ["dm2 = dclean.reset_index().groupby([\"Dataset\", \"Mode\", \"Model\", \"Fold\", 'Metric'])['Value'].aggregate('first').unstack()\n", "dm2.head"]}, {"cell_type": "markdown", "id": "19bd28a6-ad67-442d-98b1-507b13cf4c05", "metadata": {}, "source": ["## Computes the correlation between metrics and plot a heatmap below"]}, {"cell_type": "code", "execution_count": null, "id": "16cb4036-3550-4844-b4df-a2844f740d39", "metadata": {"tags": []}, "outputs": [], "source": ["correlation_matrix = dm2.corr()\n", "correlation_matrix"]}, {"cell_type": "code", "execution_count": null, "id": "6146a61a-1f5d-4195-bfb1-0c13c300786a", "metadata": {"tags": []}, "outputs": [], "source": ["# plt.figure(figsize=(10,8))\n", "\n", "fig = sns.clustermap(correlation_matrix, annot=True, fmt=\".2f\", linewidths=.5, cmap=\"Blues\", cbar_pos=None, dendrogram_ratio=0,\n", "                    figsize=(10, 8),)\n", "fig.fig.suptitle(\"Matriz de correlação das métricas\", fontsize=18, y=1.02)\n", "plt.show()\n", "fig.savefig(\"metric_corr.png\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}