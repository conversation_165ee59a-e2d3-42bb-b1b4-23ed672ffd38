#!/usr/bin/env python3
"""
Test Dataset Usage Example

This script demonstrates how to load and use the generated test dataset
with the well log imputation benchmark system.

Usage:
    python test_dataset_usage.py --dataset_name test_dataset --fold 0
"""

import numpy as np
import json
import os
import sys
import argparse
from pathlib import Path

# Add parent directory to path to import project modules
sys.path.append(str(Path(__file__).parent.parent))

from data.datasets import DatasetForImputation
from data.missing import mask_X


def load_test_dataset(dataset_dir: str, dataset_name: str, fold: int):
    """
    Load test dataset for a specific fold.
    
    Args:
        dataset_dir: Directory containing the dataset files
        dataset_name: Name of the dataset
        fold: Fold number to load
        
    Returns:
        Tuple of (train_data, val_data) as numpy arrays
    """
    train_file = Path(dataset_dir) / f"{dataset_name}_fold_{fold}_well_log_sliced_train.npy"
    val_file = Path(dataset_dir) / f"{dataset_name}_fold_{fold}_well_log_sliced_val.npy"
    
    if not train_file.exists():
        raise FileNotFoundError(f"Training data file not found: {train_file}")
    if not val_file.exists():
        raise FileNotFoundError(f"Validation data file not found: {val_file}")
    
    train_data = np.load(train_file)
    val_data = np.load(val_file)
    
    return train_data, val_data


def load_metadata(dataset_dir: str, dataset_name: str, fold: int):
    """
    Load metadata for a specific fold.
    
    Args:
        dataset_dir: Directory containing the dataset files
        dataset_name: Name of the dataset
        fold: Fold number to load
        
    Returns:
        Tuple of (train_metadata, val_metadata) as dictionaries
    """
    train_meta_file = Path(dataset_dir) / f"{dataset_name}_fold_{fold}_well_log_metadata_train.json"
    val_meta_file = Path(dataset_dir) / f"{dataset_name}_fold_{fold}_well_log_metadata_val.json"
    
    with open(train_meta_file, 'r') as f:
        train_metadata = json.load(f)
    with open(val_meta_file, 'r') as f:
        val_metadata = json.load(f)
    
    return train_metadata, val_metadata


def test_missing_patterns(data: np.ndarray, pattern: str = 'single'):
    """
    Test different missing patterns on the data.
    
    Args:
        data: Input data array with shape (n_samples, seq_len, n_features)
        pattern: Missing pattern to test ('single', 'block', 'profile')
        
    Returns:
        Dictionary with masked data components
    """
    print(f"\nTesting missing pattern: {pattern}")
    
    if pattern == 'single':
        X_intact, X_masked, missing_mask, indicating_mask = mask_X(
            data, mode='single', n_points=5
        )
    elif pattern == 'block':
        X_intact, X_masked, missing_mask, indicating_mask = mask_X(
            data, mode='block', b_size=[20, 50]
        )
    elif pattern == 'profile':
        X_intact, X_masked, missing_mask, indicating_mask = mask_X(
            data, mode='profile'
        )
    else:
        raise ValueError(f"Unknown pattern: {pattern}")
    
    # Calculate missing statistics
    total_values = X_intact.size
    missing_values = np.sum(indicating_mask)
    missing_percentage = (missing_values / total_values) * 100
    
    print(f"  Original data shape: {X_intact.shape}")
    print(f"  Total values: {total_values}")
    print(f"  Missing values: {missing_values}")
    print(f"  Missing percentage: {missing_percentage:.2f}%")
    
    return {
        'X_intact': X_intact,
        'X': X_masked,
        'missing_mask': missing_mask,
        'indicating_mask': indicating_mask
    }


def test_dataset_for_imputation(data_dict: dict):
    """
    Test the DatasetForImputation class with the masked data.
    
    Args:
        data_dict: Dictionary with masked data components
    """
    print("\nTesting DatasetForImputation class...")
    
    # Create dataset
    dataset = DatasetForImputation(data_dict)
    
    print(f"  Dataset length: {len(dataset)}")
    
    # Test getting a sample
    sample = dataset[0]
    print(f"  Sample components: {len(sample)}")
    print(f"  Sample shapes:")
    for i, component in enumerate(sample):
        print(f"    Component {i}: {component.shape} ({component.dtype})")
    
    return dataset


def main():
    parser = argparse.ArgumentParser(description="Test the generated dataset")
    parser.add_argument("--dataset_name", type=str, default="test_dataset",
                       help="Name of the test dataset")
    parser.add_argument("--dataset_dir", type=str, default="bkp_data",
                       help="Directory containing the dataset")
    parser.add_argument("--fold", type=int, default=0,
                       help="Fold number to test")
    parser.add_argument("--n_samples", type=int, default=10,
                       help="Number of samples to use for testing")
    
    args = parser.parse_args()
    
    print(f"Testing dataset: {args.dataset_name}")
    print(f"Dataset directory: {args.dataset_dir}")
    print(f"Fold: {args.fold}")
    
    try:
        # Load dataset
        train_data, val_data = load_test_dataset(args.dataset_dir, args.dataset_name, args.fold)
        train_metadata, val_metadata = load_metadata(args.dataset_dir, args.dataset_name, args.fold)
        
        print(f"\nDataset loaded successfully!")
        print(f"Training data shape: {train_data.shape}")
        print(f"Validation data shape: {val_data.shape}")
        print(f"Features: {train_metadata['feature_names']}")
        
        # Use a subset for testing
        test_data = val_data[:args.n_samples]
        print(f"\nUsing {args.n_samples} samples for testing: {test_data.shape}")
        
        # Test different missing patterns
        patterns = ['single', 'block', 'profile']
        
        for pattern in patterns:
            # Test missing pattern
            masked_data = test_missing_patterns(test_data, pattern)
            
            # Test DatasetForImputation
            dataset = test_dataset_for_imputation(masked_data)
            
            print(f"  ✓ Pattern '{pattern}' tested successfully")
        
        print(f"\n✅ All tests passed!")
        print(f"The test dataset is compatible with the imputation benchmark system.")
        
        # Show example of how to use with the main system
        print(f"\n📋 Usage with main system:")
        print(f"python main.py --dataset_name {args.dataset_name} --dataset_dir {args.dataset_dir} --model saits --epochs 10")
        
    except Exception as e:
        print(f"\n❌ Error testing dataset: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
