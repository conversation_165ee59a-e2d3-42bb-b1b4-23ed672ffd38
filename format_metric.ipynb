{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1834ad75-bf10-4a4b-94b2-213fb90bb045", "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "import pandas as pd"]}, {"cell_type": "markdown", "id": "31f25cd5-2620-419f-b354-387bbe534ea0", "metadata": {}, "source": ["## Get block\n", "The default training logs containg a block of text at the end that includes all metrics for all experiments and folds. This function gets and returns this block"]}, {"cell_type": "code", "execution_count": 2, "id": "bd6efa2b-58dd-48bf-9604-1bfab578c04e", "metadata": {"tags": []}, "outputs": [], "source": ["def get_metric_block(text):\n", "    blocks = []\n", "\n", "    block = []\n", "    c = 0\n", "    for line in text.split(\"\\n\"):\n", "        if len(line) == 0:\n", "            if len(block) > 0:\n", "                blocks.append(block)\n", "            block = []\n", "        else:\n", "            block.append(line)\n", "            \n", "    return blocks[-1]"]}, {"cell_type": "markdown", "id": "b414c6f8-c459-494c-b0be-f22e9907c42f", "metadata": {}, "source": ["## Get metrics\n", "Given the text block obtained above, this function reads the text and parse it to float arrays for each metric and scenario"]}, {"cell_type": "code", "execution_count": 3, "id": "3ff178b0-c2bd-4d9d-9d60-81b16be24b9e", "metadata": {"tags": []}, "outputs": [], "source": ["def get_metrics(metric_block):\n", "    data = []\n", "\n", "    for metric in metric_block:\n", "        metric_label,metric_data = metric.split(\":\")\n", "        \n", "        metric_data = metric_data.split(\"\\t\")\n", "        metric_data = [float(i) for i in metric_data]\n", "\n", "        if metric_label == \"training-time\":\n", "            dataset_part = \"validation\"\n", "            metric_name = \"time\"\n", "            type_data = \"time\"\n", "        else:\n", "            dataset_part,metric_name,type_data = metric_label.split(\"-\")\n", "       \n", "        i = 0\n", "        for metri_data_i in metric_data:\n", "            metric_input = [dataset_part,metric_name,type_data,metri_data_i,i]\n", "\n", "            data.append(metric_input)\n", "            \n", "            i +=1\n", "\n", "    return data"]}, {"cell_type": "markdown", "id": "e60e7a9a-bab5-4963-b6f2-9936d7ba8147", "metadata": {}, "source": ["## Creating a csv\n", "The following blocks reads all files from a folder and adds it to a pandas Dataframe including model, dataset, metric, missing pattern (Mode), and fold\n", "then saves the dataframe in a csv file"]}, {"cell_type": "code", "execution_count": 4, "id": "194f984b-35f5-40f4-b3b3-24f00a1b639a", "metadata": {"tags": []}, "outputs": [], "source": ["dataset = []\n", "for path in os.listdir(\"trained_models\"):\n", "    if path.split(\".\")[-1] != \"log\":\n", "        continue\n", "    if \"svm\" in path: continue # Here it is ignoring logs from svm model\n", "    with open(\"trained_models/{}\".format(path)) as file:\n", "        text = file.read()\n", "    \n", "    path = path.split(\".log\")[0]\n", "    model_name,dataset_name = path.split(\"_\")\n", "    \n", "    try:\n", "        metric_block = get_metric_block(text)\n", "        data_i = get_metrics(metric_block)\n", "    except:\n", "        print(path)\n", "        continue\n", "        \n", "    data_i = [[model_name,dataset_name] + i for i in data_i]\n", "    \n", "    dataset = dataset + data_i"]}, {"cell_type": "code", "execution_count": 5, "id": "e293b48b-de3c-4109-b14e-b3d50bf483a8", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Model</th>\n", "      <th>Dataset</th>\n", "      <th>Division type</th>\n", "      <th>Metric</th>\n", "      <th>Mode</th>\n", "      <th>Value</th>\n", "      <th>Fold</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>saits</td>\n", "      <td>teapot</td>\n", "      <td>validation</td>\n", "      <td>time</td>\n", "      <td>time</td>\n", "      <td>734.0000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>saits</td>\n", "      <td>teapot</td>\n", "      <td>validation</td>\n", "      <td>time</td>\n", "      <td>time</td>\n", "      <td>859.0000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>saits</td>\n", "      <td>teapot</td>\n", "      <td>validation</td>\n", "      <td>time</td>\n", "      <td>time</td>\n", "      <td>643.0000</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>saits</td>\n", "      <td>teapot</td>\n", "      <td>validation</td>\n", "      <td>time</td>\n", "      <td>time</td>\n", "      <td>681.0000</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>saits</td>\n", "      <td>teapot</td>\n", "      <td>validation</td>\n", "      <td>time</td>\n", "      <td>time</td>\n", "      <td>659.0000</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2830</th>\n", "      <td>rf</td>\n", "      <td>taranaki</td>\n", "      <td>validation</td>\n", "      <td>cc</td>\n", "      <td>profile</td>\n", "      <td>0.3634</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2831</th>\n", "      <td>rf</td>\n", "      <td>taranaki</td>\n", "      <td>validation</td>\n", "      <td>cc</td>\n", "      <td>profile</td>\n", "      <td>0.3164</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2832</th>\n", "      <td>rf</td>\n", "      <td>taranaki</td>\n", "      <td>validation</td>\n", "      <td>cc</td>\n", "      <td>profile</td>\n", "      <td>0.3345</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2833</th>\n", "      <td>rf</td>\n", "      <td>taranaki</td>\n", "      <td>validation</td>\n", "      <td>cc</td>\n", "      <td>profile</td>\n", "      <td>0.3315</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2834</th>\n", "      <td>rf</td>\n", "      <td>taranaki</td>\n", "      <td>validation</td>\n", "      <td>cc</td>\n", "      <td>profile</td>\n", "      <td>0.3212</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2835 rows × 7 columns</p>\n", "</div>"], "text/plain": ["      Model   Dataset Division type Metric     Mode     Value  Fold\n", "0     saits    teapot    validation   time     time  734.0000     0\n", "1     saits    teapot    validation   time     time  859.0000     1\n", "2     saits    teapot    validation   time     time  643.0000     2\n", "3     saits    teapot    validation   time     time  681.0000     3\n", "4     saits    teapot    validation   time     time  659.0000     4\n", "...     ...       ...           ...    ...      ...       ...   ...\n", "2830     rf  taranaki    validation     cc  profile    0.3634     0\n", "2831     rf  taranaki    validation     cc  profile    0.3164     1\n", "2832     rf  taranaki    validation     cc  profile    0.3345     2\n", "2833     rf  taranaki    validation     cc  profile    0.3315     3\n", "2834     rf  taranaki    validation     cc  profile    0.3212     4\n", "\n", "[2835 rows x 7 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset = pd.DataFrame(dataset,columns = [\"Model\",\"Dataset\",\"Division type\",\"Metric\",\"Mode\",\"Value\",\"Fold\"])\n", "dataset"]}, {"cell_type": "code", "execution_count": 6, "id": "d7df1fc4-f408-4b0b-835c-5363e71efc7c", "metadata": {"tags": []}, "outputs": [], "source": ["dataset.to_csv(\"metrics.csv\",index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}