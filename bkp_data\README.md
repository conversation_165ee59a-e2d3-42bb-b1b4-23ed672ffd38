# Test Dataset for Well Log Imputation Benchmark

This directory contains tools to create and test synthetic datasets for the well log imputation benchmark system.

## Files

- `create_test_dataset.py` - <PERSON><PERSON>t to generate synthetic well log datasets
- `test_dataset_usage.py` - <PERSON><PERSON>t to test the generated datasets
- `README.md` - This documentation file

## Quick Start

### 1. Generate a Test Dataset

```bash
# Generate a small test dataset (default: 1000 samples, 5 folds)
python bkp_data/create_test_dataset.py

# Generate a custom dataset
python bkp_data/create_test_dataset.py \
    --dataset_name my_test_dataset \
    --n_samples 2000 \
    --n_folds 3 \
    --seq_len 256 \
    --output_dir bkp_data
```

### 2. Test the Generated Dataset

```bash
# Test the default dataset
python bkp_data/test_dataset_usage.py

# Test a specific dataset and fold
python bkp_data/test_dataset_usage.py \
    --dataset_name my_test_dataset \
    --fold 0 \
    --n_samples 5
```

### 3. Use with the Main Benchmark System

```bash
# Run imputation benchmark with the test dataset
python main.py \
    --dataset_name test_dataset \
    --dataset_dir bkp_data \
    --model saits \
    --epochs 10 \
    --batch_size 16
```

## Generated Dataset Structure

The script creates a complete dataset structure compatible with the benchmark system:

```
bkp_data/
├── test_dataset_fold_0_well_log_sliced_train.npy          # Training data for fold 0
├── test_dataset_fold_0_well_log_sliced_val.npy            # Validation data for fold 0
├── test_dataset_fold_0_well_log_metadata_train.json       # Training metadata for fold 0
├── test_dataset_fold_0_well_log_metadata_val.json         # Validation metadata for fold 0
├── test_dataset_fold_0_well_log_slices_meta_train.json    # Training slices metadata for fold 0
├── test_dataset_fold_0_well_log_slices_meta_val.json      # Validation slices metadata for fold 0
├── ... (similar files for other folds)
```

## Dataset Features

The synthetic dataset includes four standard well log features:

1. **GR** (Gamma Ray) - Range: 0-200 API units
2. **DTC** (Delta Time Compressional) - Range: 40-200 μs/ft
3. **RHOB** (Bulk Density) - Range: 1.5-3.0 g/cm³
4. **NPHI** (Neutron Porosity) - Range: 0-0.6 (0-60%)

## Data Characteristics

- **Realistic patterns**: The synthetic data includes depth-dependent trends and geological layering patterns
- **Proper correlations**: Features show realistic correlations (e.g., density increases with depth)
- **Noise**: Appropriate noise levels are added to simulate real-world measurements
- **Format compatibility**: Data format matches exactly what the benchmark system expects

## Command Line Options

### create_test_dataset.py

- `--dataset_name`: Name of the test dataset (default: "test_dataset")
- `--n_samples`: Number of samples to generate (default: 1000)
- `--seq_len`: Length of each sequence (default: 256)
- `--n_folds`: Number of cross-validation folds (default: 5)
- `--output_dir`: Output directory for the dataset (default: "bkp_data")
- `--seed`: Random seed for reproducibility (default: 42)

### test_dataset_usage.py

- `--dataset_name`: Name of the test dataset (default: "test_dataset")
- `--dataset_dir`: Directory containing the dataset (default: "bkp_data")
- `--fold`: Fold number to test (default: 0)
- `--n_samples`: Number of samples to use for testing (default: 10)

## Example Output

When you run the test script, you should see output like:

```
Testing dataset: test_dataset
Dataset directory: bkp_data
Fold: 0

Dataset loaded successfully!
Training data shape: (800, 256, 4)
Validation data shape: (200, 256, 4)
Features: ['GR', 'DTC', 'RHOB', 'NPHI']

Using 10 samples for testing: (10, 256, 4)

Testing missing pattern: single
  Original data shape: (10, 256, 4)
  Total values: 10240
  Missing values: 512
  Missing percentage: 5.00%

Testing DatasetForImputation class...
  Dataset length: 10
  Sample components: 5
  Sample shapes:
    Component 0: torch.Size([]) (torch.int64)
    Component 1: torch.Size([4, 256]) (torch.float32)
    Component 2: torch.Size([4, 256]) (torch.float32)
    Component 3: torch.Size([4, 256]) (torch.float32)
    Component 4: torch.Size([4, 256]) (torch.float32)
  ✓ Pattern 'single' tested successfully

... (similar output for 'block' and 'profile' patterns)

✅ All tests passed!
The test dataset is compatible with the imputation benchmark system.

📋 Usage with main system:
python main.py --dataset_name test_dataset --dataset_dir bkp_data --model saits --epochs 10
```

## Notes

- The synthetic data is designed to be realistic but is not based on actual geological data
- The dataset is suitable for testing and development purposes
- For production use, replace with real well log datasets
- The generated files follow the exact naming convention expected by the benchmark system
- All metadata files are properly formatted and include necessary information for the system to work correctly
