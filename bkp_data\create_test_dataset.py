#!/usr/bin/env python3
"""
Test Dataset Generator for Well Log Imputation Benchmark

This script creates synthetic test datasets that match the expected format
for the well log imputation benchmark system.

The generated datasets include:
- Synthetic well log data with realistic patterns
- Proper numpy array format with shape (n_samples, seq_len, n_features)
- Standard well log features: GR, DTC, RHOB, NPHI
- Multiple folds for cross-validation
- Associated metadata files

Usage:
    python create_test_dataset.py --dataset_name test_dataset --n_samples 1000 --n_folds 5
"""

import numpy as np
import json
import os
import argparse
from pathlib import Path
from typing import List, Dict, Any


def generate_synthetic_well_log_data(n_samples: int, seq_len: int = 256, n_features: int = 4, seed: int = 42) -> np.ndarray:
    """
    Generate synthetic well log data with realistic patterns.
    
    Args:
        n_samples: Number of sequences to generate
        seq_len: Length of each sequence (default: 256)
        n_features: Number of features (default: 4 for G<PERSON>, <PERSON>TC, RHOB, NPH<PERSON>)
        seed: Random seed for reproducibility
        
    Returns:
        numpy array with shape (n_samples, seq_len, n_features)
    """
    np.random.seed(seed)
    
    # Initialize data array
    data = np.zeros((n_samples, seq_len, n_features))
    
    for i in range(n_samples):
        # Generate depth-dependent trends
        depth_trend = np.linspace(0, 1, seq_len)
        
        # Feature 0: Gamma Ray (GR) - typically 0-200 API units
        # Add some geological layering patterns
        gr_base = 50 + 100 * np.sin(depth_trend * 4 * np.pi) + 30 * np.random.randn(seq_len)
        gr_base = np.clip(gr_base, 0, 200)
        data[i, :, 0] = gr_base
        
        # Feature 1: Delta Time Compressional (DTC) - typically 40-200 μs/ft
        # Inversely correlated with depth (compaction)
        dtc_base = 140 - 50 * depth_trend + 20 * np.random.randn(seq_len)
        dtc_base = np.clip(dtc_base, 40, 200)
        data[i, :, 1] = dtc_base
        
        # Feature 2: Bulk Density (RHOB) - typically 1.5-3.0 g/cm³
        # Positively correlated with depth
        rhob_base = 2.0 + 0.5 * depth_trend + 0.2 * np.random.randn(seq_len)
        rhob_base = np.clip(rhob_base, 1.5, 3.0)
        data[i, :, 2] = rhob_base
        
        # Feature 3: Neutron Porosity (NPHI) - typically 0-0.6 (60%)
        # Add some porosity variations
        nphi_base = 0.3 - 0.2 * depth_trend + 0.1 * np.random.randn(seq_len)
        nphi_base = np.clip(nphi_base, 0, 0.6)
        data[i, :, 3] = nphi_base
    
    return data


def create_fold_split(n_samples: int, n_folds: int, seed: int = 42) -> List[Dict[str, List[int]]]:
    """
    Create fold splits for cross-validation.
    
    Args:
        n_samples: Total number of samples
        n_folds: Number of folds
        seed: Random seed
        
    Returns:
        List of dictionaries with 'train' and 'val' indices for each fold
    """
    np.random.seed(seed)
    indices = np.random.permutation(n_samples)
    fold_size = n_samples // n_folds
    
    folds = []
    for fold in range(n_folds):
        val_start = fold * fold_size
        val_end = (fold + 1) * fold_size if fold < n_folds - 1 else n_samples
        
        val_indices = indices[val_start:val_end].tolist()
        train_indices = np.concatenate([indices[:val_start], indices[val_end:]]).tolist()
        
        folds.append({
            'train': train_indices,
            'val': val_indices
        })
    
    return folds


def create_metadata(dataset_name: str, fold: int, partition: str, 
                   n_samples: int, seq_len: int, features: List[str]) -> Dict[str, Any]:
    """
    Create metadata dictionary for a dataset partition.
    
    Args:
        dataset_name: Name of the dataset
        fold: Fold number
        partition: 'train' or 'val'
        n_samples: Number of samples in this partition
        seq_len: Sequence length
        features: List of feature names
        
    Returns:
        Metadata dictionary
    """
    return {
        "dataset_name": dataset_name,
        "fold": fold,
        "partition": partition,
        "n_samples": n_samples,
        "sequence_length": seq_len,
        "n_features": len(features),
        "feature_names": features,
        "data_shape": [n_samples, seq_len, len(features)],
        "description": f"Synthetic test dataset for well log imputation benchmark - fold {fold} {partition} partition"
    }


def create_slices_metadata(dataset_name: str, fold: int, partition: str, 
                          indices: List[int], seq_len: int) -> Dict[str, Any]:
    """
    Create per-well metadata for slices.
    
    Args:
        dataset_name: Name of the dataset
        fold: Fold number
        partition: 'train' or 'val'
        indices: Sample indices in this partition
        seq_len: Sequence length
        
    Returns:
        Slices metadata dictionary
    """
    wells_meta = {}
    for i, idx in enumerate(indices):
        well_name = f"synthetic_well_{idx:04d}"
        wells_meta[well_name] = {
            "well_id": idx,
            "n_slices": 1,  # Each sample is one slice
            "slice_length": seq_len,
            "slice_indices": [i]  # Index in the partition array
        }
    
    return {
        "dataset_name": dataset_name,
        "fold": fold,
        "partition": partition,
        "n_wells": len(indices),
        "wells": wells_meta
    }


def main():
    parser = argparse.ArgumentParser(description="Generate synthetic test dataset for well log imputation")
    parser.add_argument("--dataset_name", type=str, default="test_dataset", 
                       help="Name of the test dataset")
    parser.add_argument("--n_samples", type=int, default=1000, 
                       help="Number of samples to generate")
    parser.add_argument("--seq_len", type=int, default=256, 
                       help="Length of each sequence")
    parser.add_argument("--n_folds", type=int, default=5, 
                       help="Number of cross-validation folds")
    parser.add_argument("--output_dir", type=str, default="bkp_data", 
                       help="Output directory for the dataset")
    parser.add_argument("--seed", type=int, default=42, 
                       help="Random seed for reproducibility")
    
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Standard well log features
    features = ['GR', 'DTC', 'RHOB', 'NPHI']
    
    print(f"Generating synthetic dataset '{args.dataset_name}' with {args.n_samples} samples...")
    
    # Generate synthetic data
    data = generate_synthetic_well_log_data(
        n_samples=args.n_samples, 
        seq_len=args.seq_len, 
        n_features=len(features),
        seed=args.seed
    )
    
    print(f"Generated data shape: {data.shape}")
    
    # Create fold splits
    folds = create_fold_split(args.n_samples, args.n_folds, args.seed)
    
    # Save data for each fold
    for fold_idx, fold_data in enumerate(folds):
        print(f"Processing fold {fold_idx}...")
        
        # Extract train and validation data
        train_indices = fold_data['train']
        val_indices = fold_data['val']
        
        train_data = data[train_indices]
        val_data = data[val_indices]
        
        # Save numpy arrays
        train_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_sliced_train.npy"
        val_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_sliced_val.npy"
        
        np.save(train_file, train_data)
        np.save(val_file, val_data)
        
        # Create and save metadata
        train_metadata = create_metadata(
            args.dataset_name, fold_idx, 'train', 
            len(train_indices), args.seq_len, features
        )
        val_metadata = create_metadata(
            args.dataset_name, fold_idx, 'val', 
            len(val_indices), args.seq_len, features
        )
        
        train_meta_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_metadata_train.json"
        val_meta_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_metadata_val.json"
        
        with open(train_meta_file, 'w') as f:
            json.dump(train_metadata, f, indent=2)
        with open(val_meta_file, 'w') as f:
            json.dump(val_metadata, f, indent=2)
        
        # Create and save slices metadata
        train_slices_meta = create_slices_metadata(
            args.dataset_name, fold_idx, 'train', train_indices, args.seq_len
        )
        val_slices_meta = create_slices_metadata(
            args.dataset_name, fold_idx, 'val', val_indices, args.seq_len
        )
        
        train_slices_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_slices_meta_train.json"
        val_slices_file = output_dir / f"{args.dataset_name}_fold_{fold_idx}_well_log_slices_meta_val.json"
        
        with open(train_slices_file, 'w') as f:
            json.dump(train_slices_meta, f, indent=2)
        with open(val_slices_file, 'w') as f:
            json.dump(val_slices_meta, f, indent=2)
        
        print(f"  Fold {fold_idx}: {len(train_indices)} train, {len(val_indices)} val samples")
    
    print(f"\nDataset creation complete!")
    print(f"Files saved to: {output_dir}")
    print(f"Dataset name: {args.dataset_name}")
    print(f"Total samples: {args.n_samples}")
    print(f"Sequence length: {args.seq_len}")
    print(f"Features: {features}")
    print(f"Number of folds: {args.n_folds}")


if __name__ == "__main__":
    main()
